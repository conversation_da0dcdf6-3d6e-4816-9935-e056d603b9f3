/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!
// NOTE: json tags are required.  Any new fields you add must have json tags for the fields to be serialized.

// WorkspaceSpec defines the desired state of Workspace.
type WorkspaceSpec struct {
	// INSERT ADDITIONAL SPEC FIELDS - desired state of cluster
	// Important: Run "make" to regenerate code after modifying this file

	// Image specifies the container image to use for the workspace pod
	// If not specified, defaults to nginx:latest
	Image string `json:"image,omitempty"`

	// Resources specifies the resource requirements for the workspace pod
	Resources *ResourceRequirements `json:"resources,omitempty"`

	// NodeSelector specifies node selection constraints for the workspace pod
	NodeSelector map[string]string `json:"nodeSelector,omitempty"`
}

// ResourceRequirements defines the resource requirements for a workspace pod
type ResourceRequirements struct {
	// Limits describes the maximum amount of compute resources allowed
	Limits map[string]string `json:"limits,omitempty"`
	// Requests describes the minimum amount of compute resources required
	Requests map[string]string `json:"requests,omitempty"`
}

// WorkspaceStatus defines the observed state of Workspace.
type WorkspaceStatus struct {
	// INSERT ADDITIONAL STATUS FIELD - define observed state of cluster
	// Important: Run "make" to regenerate code after modifying this file

	// Phase represents the current phase of the workspace
	Phase WorkspacePhase `json:"phase,omitempty"`

	// PodName is the name of the created pod
	PodName string `json:"podName,omitempty"`

	// NodeName is the name of the node where the pod is running
	NodeName string `json:"nodeName,omitempty"`

	// ImageUsed is the actual image used for the pod
	ImageUsed string `json:"imageUsed,omitempty"`

	// Message provides additional information about the workspace status
	Message string `json:"message,omitempty"`

	// LastUpdateTime is the last time the status was updated
	LastUpdateTime *metav1.Time `json:"lastUpdateTime,omitempty"`
}

// WorkspacePhase represents the phase of a workspace
type WorkspacePhase string

const (
	// WorkspacePending means the workspace is being created
	WorkspacePending WorkspacePhase = "Pending"
	// WorkspaceRunning means the workspace pod is running
	WorkspaceRunning WorkspacePhase = "Running"
	// WorkspaceFailed means the workspace creation failed
	WorkspaceFailed WorkspacePhase = "Failed"
	// WorkspaceDeleting means the workspace is being deleted
	WorkspaceDeleting WorkspacePhase = "Deleting"
)

// +kubebuilder:object:root=true
// +kubebuilder:subresource:status

// Workspace is the Schema for the workspaces API.
type Workspace struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   WorkspaceSpec   `json:"spec,omitempty"`
	Status WorkspaceStatus `json:"status,omitempty"`
}

// +kubebuilder:object:root=true

// WorkspaceList contains a list of Workspace.
type WorkspaceList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []Workspace `json:"items"`
}

func init() {
	SchemeBuilder.Register(&Workspace{}, &WorkspaceList{})
}
