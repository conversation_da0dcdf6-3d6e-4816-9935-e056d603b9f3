# Workspace Container Operator

一个 Kubernetes Operator，用于直接管理 Pod 的创建和删除，支持 Docker Registry 镜像检查和容器状态保存。

## 功能特性

- **智能镜像选择**: 自动检查 Docker Registry (*************:5000) 中是否存在与 Workspace 名称相同的镜像
- **直接 Pod 管理**: 直接创建和管理 Pod，不使用 Deployment
- **容器状态保存**: 删除 Pod 前自动 commit 容器状态并推送到 Registry
- **状态跟踪**: 完整的 Workspace 生命周期状态跟踪

## 工作流程

### Pod 创建流程
1. 检查 Docker Registry 是否存在名为 `workspace-name` 的镜像
   ```bash
   curl "*************:5000/v2/workspace-jinxq-test/tags/list"
   ```
2. 如果存在，使用 `*************:5000/workspace-name:latest`
3. 如果不存在，使用默认镜像 `nginx:latest` 或 spec 中指定的镜像

### Pod 删除流程
1. 获取 Pod 所在节点的 IP 地址
2. 通过节点的 Docker API (端口 22375) commit 容器当前状态
3. 将 commit 的镜像推送到 Registry 作为 `*************:5000/workspace-name:latest`
4. 正常删除 Pod 和相关资源

## 快速开始

### 前置条件
- Go 1.24.0+
- Docker 17.03+
- kubectl v1.11.3+
- 访问 Kubernetes v1.11.3+ 集群
- Docker Registry 运行在 *************:5000
- 集群节点的 Docker API 暴露在 22375 端口

### 安装和部署

1. **安装 CRDs:**
```sh
make install
```

2. **构建并运行 Operator:**
```sh
make run
```

或者在集群中部署:
```sh
make docker-build docker-push IMG=<your-registry>/container-operator:tag
make deploy IMG=<your-registry>/container-operator:tag
```

3. **创建 Workspace 实例:**
```sh
kubectl apply -f config/samples/workspace_v1_workspace.yaml
```

### 使用示例

创建一个名为 `workspace-jinxq-test` 的 Workspace:

```yaml
apiVersion: workspace.chuangcache.com/v1
kind: Workspace
metadata:
  name: workspace-jinxq-test
  namespace: default
spec:
  image: "nginx:latest"  # 备用镜像
  resources:
    limits:
      cpu: "500m"
      memory: "512Mi"
    requests:
      cpu: "100m"
      memory: "128Mi"
  nodeSelector:
    kubernetes.io/os: linux
```

### 测试脚本

运行提供的测试脚本:
```sh
./test-workspace.sh
```

## API 参考

### WorkspaceSpec

| 字段 | 类型 | 描述 |
|------|------|------|
| `image` | string | 容器镜像，当 Registry 中不存在同名镜像时使用 |
| `resources` | ResourceRequirements | Pod 资源要求 |
| `nodeSelector` | map[string]string | 节点选择器 |

### WorkspaceStatus

| 字段 | 类型 | 描述 |
|------|------|------|
| `phase` | WorkspacePhase | 当前阶段 (Pending/Running/Failed/Deleting) |
| `podName` | string | 创建的 Pod 名称 |
| `nodeName` | string | Pod 运行的节点名称 |
| `imageUsed` | string | 实际使用的镜像 |
| `message` | string | 状态描述信息 |
| `lastUpdateTime` | *metav1.Time | 最后更新时间 |

## 清理

删除 Workspace 实例:
```sh
kubectl delete workspace workspace-jinxq-test
```

卸载 CRDs:
```sh
make uninstall
```

卸载 Operator:
```sh
make undeploy
```

## 开发

运行测试:
```sh
make test
```

生成代码:
```sh
make generate
make manifests
```

## 注意事项

1. 确保 Docker Registry (*************:5000) 可访问
2. 确保集群节点的 Docker API 在 22375 端口可访问
3. 删除操作会尝试保存容器状态，如果失败不会阻止删除过程
4. Operator 需要适当的 RBAC 权限来管理 Pod 和访问节点信息

## 项目结构

```
container-operator/
├── api/v1/                    # CRD 定义
│   ├── workspace_types.go     # Workspace 资源类型定义
│   └── ...
├── internal/controller/       # Controller 实现
│   ├── workspace_controller.go # 主要的 reconcile 逻辑
│   └── ...
├── config/                    # Kubernetes 配置文件
│   ├── crd/bases/            # CRD manifests
│   ├── samples/              # 示例资源
│   └── ...
├── test-workspace.sh         # 测试脚本
└── README.md                 # 项目文档
```

更多信息请参考 [Kubebuilder 文档](https://book.kubebuilder.io/introduction.html)。
