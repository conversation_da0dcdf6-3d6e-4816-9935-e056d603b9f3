#!/bin/bash

# Test script for Workspace Operator

set -e

echo "=== Testing Workspace Operator ==="

# Function to cleanup
cleanup() {
    echo "Cleaning up..."
    kubectl delete workspace workspace-jinxq-test --ignore-not-found=true
    sleep 5
}

# Trap to ensure cleanup on exit
trap cleanup EXIT

# Clean up any existing resources first
echo "0. Cleaning up any existing resources..."
cleanup

# Build and install CRDs
echo "1. Installing CRDs..."
make install

# Build the operator
echo "2. Building operator..."
make build

# Apply the sample workspace
echo "3. Creating workspace resource..."
kubectl apply -f config/samples/workspace_v1_workspace.yaml

# Wait a bit and check status
echo "4. Waiting for workspace to be created..."
sleep 15

# Check workspace status
echo "5. Checking workspace status..."
kubectl get workspace workspace-jinxq-test -o yaml

# Check if pod was created
echo "6. Checking if pod was created..."
kubectl get pods -l workspace.chuangcache.com/name=workspace-jinxq-test

# Show pod details if exists
echo "7. Pod details:"
if kubectl get pods -l workspace.chuangcache.com/name=workspace-jinxq-test --no-headers | grep -q .; then
    kubectl describe pod -l workspace.chuangcache.com/name=workspace-jinxq-test
else
    echo "No pods found with label workspace.chuangcache.com/name=workspace-jinxq-test"
fi

# Wait a bit more and check final status
echo "8. Waiting for pod to start..."
sleep 10

echo "9. Final workspace status:"
kubectl get workspace workspace-jinxq-test -o jsonpath='{.status}' | jq .

echo "=== Test completed ==="
echo "Cleanup will happen automatically..."
