#!/bin/bash

# Test script for Workspace Operator

set -e

echo "=== Testing Workspace Operator ==="

# Build and install CRDs
echo "1. Installing CRDs..."
make install

# Build the operator
echo "2. Building operator..."
make build

# Apply the sample workspace
echo "3. Creating workspace resource..."
kubectl apply -f config/samples/workspace_v1_workspace.yaml

# Wait a bit and check status
echo "4. Waiting for workspace to be created..."
sleep 10

# Check workspace status
echo "5. Checking workspace status..."
kubectl get workspace workspace-jinxq-test -o yaml

# Check if pod was created
echo "6. Checking if pod was created..."
kubectl get pods -l workspace.chuangcache.com/name=workspace-jinxq-test

# Show pod details
echo "7. Pod details:"
kubectl describe pod -l workspace.chuangcache.com/name=workspace-jinxq-test

echo "=== Test completed ==="
echo "To clean up, run: kubectl delete workspace workspace-jinxq-test"
