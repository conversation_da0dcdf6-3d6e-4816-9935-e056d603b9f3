# Workspace Operator 故障排除指南

## 常见问题和解决方案

### 1. 并发更新错误

**错误信息**:
```
Operation cannot be fulfilled on workspaces.workspace.chuangcache.com "workspace-jinxq-test": the object has been modified; please apply your changes to the latest version and try again
```

**原因**: 多个 reconcile 循环同时尝试更新同一个 Workspace 资源。

**解决方案**: 
- 已在最新版本中修复，controller 现在会获取最新版本再更新
- 如果仍然出现，会自动重试

### 2. Pod 创建失败

**检查步骤**:
```bash
# 查看 workspace 状态
kubectl get workspace workspace-jinxq-test -o yaml

# 查看事件
kubectl get events --sort-by=.metadata.creationTimestamp

# 查看节点资源
kubectl describe nodes
```

**常见原因**:
- 节点资源不足
- 镜像拉取失败
- 网络策略阻止

### 3. Docker Registry 连接失败

**检查 Registry 连接**:
```bash
# 从集群内测试
kubectl run test-pod --image=busybox --rm -it -- sh
# 在 pod 内执行
wget -qO- http://*************:5000/v2/_catalog
```

**检查镜像是否存在**:
```bash
curl "http://*************:5000/v2/workspace-jinxq-test/tags/list"
```

### 4. Docker API 访问失败

**检查节点 Docker API**:
```bash
# 获取节点 IP
kubectl get nodes -o wide

# 测试 Docker API
curl http://NODE_IP:22375/version
```

**常见问题**:
- Docker API 未暴露在 22375 端口
- 防火墙阻止访问
- Docker daemon 配置问题

### 5. 容器 Commit 失败

**检查容器状态**:
```bash
# 查看 pod 详情
kubectl describe pod POD_NAME

# 检查容器 ID
kubectl get pod POD_NAME -o jsonpath='{.status.containerStatuses[0].containerID}'
```

**手动测试 Commit**:
```bash
# 在节点上执行
docker commit CONTAINER_ID test-image:latest
docker tag test-image:latest *************:5000/test-image:latest
docker push *************:5000/test-image:latest
```

## 调试命令

### 查看 Operator 日志
```bash
# 如果使用 make run
# 日志直接输出到终端

# 如果部署在集群中
kubectl logs -n container-operator-system deployment/container-operator-controller-manager -f
```

### 查看 Workspace 详细信息
```bash
kubectl get workspace workspace-jinxq-test -o yaml
kubectl describe workspace workspace-jinxq-test
```

### 查看相关 Pod
```bash
kubectl get pods -l workspace.chuangcache.com/name=workspace-jinxq-test
kubectl describe pod -l workspace.chuangcache.com/name=workspace-jinxq-test
```

### 查看事件
```bash
kubectl get events --field-selector involvedObject.kind=Workspace
kubectl get events --field-selector involvedObject.kind=Pod
```

## 性能调优

### 减少并发冲突
- 增加 requeue 间隔时间
- 使用更精确的状态检查条件

### 优化网络超时
```go
// 在 controller 中调整超时设置
client := &http.Client{
    Timeout: 30 * time.Second, // 根据网络环境调整
}
```

### 资源限制
```yaml
# 为 operator 设置资源限制
resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 128Mi
```

## 清理和重置

### 完全清理
```bash
# 删除所有 workspace
kubectl delete workspaces --all

# 卸载 operator
make undeploy

# 删除 CRDs
make uninstall

# 清理 registry 中的镜像（可选）
curl -X DELETE http://*************:5000/v2/workspace-jinxq-test/manifests/DIGEST
```

### 重新部署
```bash
# 重新安装
make install
make run
```

## 监控建议

### 关键指标
- Workspace 创建成功率
- Pod 启动时间
- Registry 连接成功率
- Container commit 成功率

### 告警规则
- Workspace 长时间处于 Pending 状态
- Pod 创建失败率过高
- Registry 连接失败
- Docker API 访问失败

## 日志级别调整

在开发环境中，可以增加日志详细程度：
```bash
# 运行时添加详细日志
make run ARGS="--zap-log-level=debug"
```

## 联系支持

如果问题仍然存在，请提供以下信息：
1. Kubernetes 版本
2. 节点配置
3. Operator 日志
4. Workspace 和 Pod 的 YAML 输出
5. 网络配置详情
