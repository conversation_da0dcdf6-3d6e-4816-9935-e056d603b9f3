apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: docker-api-proxy
  labels:
    app: docker-api-proxy
spec:
  selector:
    matchLabels:
      app: docker-api-proxy
  template:
    metadata:
      labels:
        app: docker-api-proxy
    spec:
      containers:
      - name: socat
        image: docker.1ms.run/alpine/socat:*******
        ports:  
        - containerPort: 2375
          hostPort: 22375  
          hostIP: "0.0.0.0"
        securityContext:
          privileged: true 
        volumeMounts:
        - name: docker-sock
          mountPath: /host/var/run/docker.sock
        command: ["sh", "-c"]
        args:
          - |
            socat tcp-listen:2375,fork,reuseaddr,bind=0.0.0.0 unix-connect:/host/var/run/docker.sock
      restartPolicy: Always
      volumes:
      - name: docker-sock
        hostPath:
          path: /var/run/docker.sock
