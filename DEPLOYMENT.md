# Workspace Operator 部署指南

本文档详细说明如何部署和使用 Workspace Operator。

## 前置条件

### 1. 环境要求
- Kubernetes 集群 v1.11.3+
- kubectl 已配置并可访问集群
- Docker Registry 运行在 `*************:5000`
- 集群节点的 Docker API 暴露在端口 `22375`

### 2. 验证环境

检查 Docker Registry 是否可访问：
```bash
curl http://*************:5000/v2/_catalog
```

检查节点 Docker API（替换 NODE_IP 为实际节点 IP）：
```bash
curl http://NODE_IP:22375/version
```

## 部署步骤

### 1. 克隆项目
```bash
git clone <your-repo-url>
cd container-operator
```

### 2. 安装 CRDs
```bash
make install
```

验证 CRD 安装：
```bash
kubectl get crd workspaces.workspace.chuangcache.com
```

### 3. 部署 Operator

#### 方式一：本地运行（开发模式）
```bash
make run
```

#### 方式二：集群内部署（生产模式）
```bash
# 构建并推送镜像
make docker-build docker-push IMG=<your-registry>/container-operator:v1.0.0

# 部署到集群
make deploy IMG=<your-registry>/container-operator:v1.0.0
```

验证部署：
```bash
kubectl get deployment -n container-operator-system
kubectl get pods -n container-operator-system
```

## 使用示例

### 1. 创建 Workspace

创建示例 Workspace：
```bash
kubectl apply -f config/samples/workspace_v1_workspace.yaml
```

或者创建自定义 Workspace：
```yaml
apiVersion: workspace.chuangcache.com/v1
kind: Workspace
metadata:
  name: my-workspace
  namespace: default
spec:
  image: "nginx:latest"
  resources:
    limits:
      cpu: "1000m"
      memory: "1Gi"
    requests:
      cpu: "200m"
      memory: "256Mi"
  nodeSelector:
    kubernetes.io/os: linux
```

### 2. 监控 Workspace 状态

查看 Workspace 状态：
```bash
kubectl get workspace workspace-jinxq-test -o yaml
```

查看创建的 Pod：
```bash
kubectl get pods -l workspace.chuangcache.com/name=workspace-jinxq-test
```

查看详细事件：
```bash
kubectl describe workspace workspace-jinxq-test
```

### 3. 删除 Workspace

删除 Workspace（会自动保存容器状态）：
```bash
kubectl delete workspace workspace-jinxq-test
```

## 镜像选择逻辑

Operator 按以下优先级选择镜像：

1. **检查自定义镜像**：查询 `*************:5000/workspace-name:latest`
   ```bash
   curl "*************:5000/v2/workspace-jinxq-test/tags/list"
   ```

2. **使用自定义镜像**：如果存在，使用 `*************:5000/workspace-name:latest`

3. **使用指定镜像**：如果自定义镜像不存在，使用 `spec.image` 字段指定的镜像

4. **使用默认镜像**：如果都没有指定，使用 `nginx:latest`

## 容器状态保存

当删除 Workspace 时，Operator 会：

1. 获取 Pod 所在节点的 IP 地址
2. 通过 Docker API commit 容器当前状态
3. 推送镜像到 Registry 作为 `*************:5000/workspace-name:latest`
4. 删除 Pod 和相关资源

## 故障排除

### 1. Operator 无法启动
检查 RBAC 权限：
```bash
kubectl get clusterrole manager-role
kubectl get clusterrolebinding manager-rolebinding
```

### 2. Pod 创建失败
检查节点资源：
```bash
kubectl describe nodes
kubectl get events --sort-by=.metadata.creationTimestamp
```

### 3. 镜像检查失败
验证 Registry 连接：
```bash
# 从集群内测试
kubectl run test-pod --image=busybox --rm -it -- sh
# 在 pod 内执行
wget -qO- http://*************:5000/v2/_catalog
```

### 4. Docker API 访问失败
检查节点 Docker API：
```bash
# 获取节点 IP
kubectl get nodes -o wide

# 测试 Docker API
curl http://NODE_IP:22375/version
```

### 5. 容器 commit 失败
检查容器状态和权限：
```bash
# 查看 pod 详情
kubectl describe pod POD_NAME

# 检查容器 ID
kubectl get pod POD_NAME -o jsonpath='{.status.containerStatuses[0].containerID}'
```

## 监控和日志

### 查看 Operator 日志
```bash
# 本地运行模式
# 日志直接输出到终端

# 集群部署模式
kubectl logs -n container-operator-system deployment/container-operator-controller-manager -f
```

### 查看 Workspace 事件
```bash
kubectl get events --field-selector involvedObject.kind=Workspace
```

## 清理

### 删除所有 Workspace
```bash
kubectl delete workspaces --all
```

### 卸载 Operator
```bash
make undeploy
```

### 删除 CRDs
```bash
make uninstall
```

## 安全注意事项

1. **Docker API 访问**：确保 Docker API 端口 22375 只在集群内网络可访问
2. **Registry 访问**：配置适当的网络策略限制 Registry 访问
3. **RBAC 权限**：Operator 需要管理 Pod 和访问节点信息的权限
4. **镜像安全**：定期扫描 Registry 中的镜像安全漏洞

## 性能调优

1. **资源限制**：为 Operator 设置适当的资源限制
2. **并发控制**：根据集群规模调整 Controller 并发数
3. **超时设置**：根据网络环境调整 Registry 和 Docker API 超时时间
