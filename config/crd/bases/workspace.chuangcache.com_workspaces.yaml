---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.18.0
  name: workspaces.workspace.chuangcache.com
spec:
  group: workspace.chuangcache.com
  names:
    kind: Workspace
    listKind: WorkspaceList
    plural: workspaces
    singular: workspace
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: Workspace is the Schema for the workspaces API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: WorkspaceSpec defines the desired state of Workspace.
            properties:
              image:
                description: |-
                  Image specifies the container image to use for the workspace pod
                  If not specified, defaults to nginx:latest
                type: string
              nodeSelector:
                additionalProperties:
                  type: string
                description: NodeSelector specifies node selection constraints for
                  the workspace pod
                type: object
              resources:
                description: Resources specifies the resource requirements for the
                  workspace pod
                properties:
                  limits:
                    additionalProperties:
                      type: string
                    description: Limits describes the maximum amount of compute resources
                      allowed
                    type: object
                  requests:
                    additionalProperties:
                      type: string
                    description: Requests describes the minimum amount of compute
                      resources required
                    type: object
                type: object
            type: object
          status:
            description: WorkspaceStatus defines the observed state of Workspace.
            properties:
              imageUsed:
                description: ImageUsed is the actual image used for the pod
                type: string
              lastUpdateTime:
                description: LastUpdateTime is the last time the status was updated
                format: date-time
                type: string
              message:
                description: Message provides additional information about the workspace
                  status
                type: string
              nodeName:
                description: NodeName is the name of the node where the pod is running
                type: string
              phase:
                description: Phase represents the current phase of the workspace
                type: string
              podName:
                description: PodName is the name of the created pod
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
