# This rule is not used by the project container-operator itself.
# It is provided to allow the cluster admin to help manage permissions for users.
#
# Grants permissions to create, update, and delete resources within the workspace.chuangcache.com.
# This role is intended for users who need to manage these resources
# but should not control RBAC or manage permissions for others.

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: container-operator
    app.kubernetes.io/managed-by: kustomize
  name: workspace-editor-role
rules:
- apiGroups:
  - workspace.chuangcache.com
  resources:
  - workspaces
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - workspace.chuangcache.com
  resources:
  - workspaces/status
  verbs:
  - get
