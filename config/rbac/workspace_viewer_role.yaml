# This rule is not used by the project container-operator itself.
# It is provided to allow the cluster admin to help manage permissions for users.
#
# Grants read-only access to workspace.chuangcache.com resources.
# This role is intended for users who need visibility into these resources
# without permissions to modify them. It is ideal for monitoring purposes and limited-access viewing.

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: container-operator
    app.kubernetes.io/managed-by: kustomize
  name: workspace-viewer-role
rules:
- apiGroups:
  - workspace.chuangcache.com
  resources:
  - workspaces
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - workspace.chuangcache.com
  resources:
  - workspaces/status
  verbs:
  - get
