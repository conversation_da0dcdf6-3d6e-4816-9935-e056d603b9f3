# This rule is not used by the project container-operator itself.
# It is provided to allow the cluster admin to help manage permissions for users.
#
# Grants full permissions ('*') over workspace.chuangcache.com.
# This role is intended for users authorized to modify roles and bindings within the cluster,
# enabling them to delegate specific permissions to other users or groups as needed.

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: container-operator
    app.kubernetes.io/managed-by: kustomize
  name: workspace-admin-role
rules:
- apiGroups:
  - workspace.chuangcache.com
  resources:
  - workspaces
  verbs:
  - '*'
- apiGroups:
  - workspace.chuangcache.com
  resources:
  - workspaces/status
  verbs:
  - get
