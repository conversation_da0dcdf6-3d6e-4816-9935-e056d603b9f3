---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: manager-role
rules:
- apiGroups:
  - ""
  resources:
  - nodes
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - pods
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - workspace.chuangcache.com
  resources:
  - workspaces
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - workspace.chuangcache.com
  resources:
  - workspaces/finalizers
  verbs:
  - update
- apiGroups:
  - workspace.chuangcache.com
  resources:
  - workspaces/status
  verbs:
  - get
  - patch
  - update
