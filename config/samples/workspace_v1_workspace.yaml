apiVersion: workspace.chuangcache.com/v1
kind: Workspace
metadata:
  labels:
    app.kubernetes.io/name: container-operator
    app.kubernetes.io/managed-by: kustomize
  name: workspace-jinxq-xxxxx
  namespace: default
spec:
  # Image will be determined automatically:
  # 1. Check if 192.168.10.16:5000/workspace-jinxq-test:latest exists
  # 2. If yes, use that image
  # 3. If no, use nginx:latest (default)
  image: "nginx:latest"  # fallback image

  # Optional resource requirements
  resources:
    limits:
      cpu: "500m"
      memory: "512Mi"
    requests:
      cpu: "100m"
      memory: "128Mi"

  # Optional node selector
  nodeSelector:
    kubernetes.io/os: linux
