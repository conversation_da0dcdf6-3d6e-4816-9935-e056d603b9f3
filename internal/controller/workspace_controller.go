/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	logf "sigs.k8s.io/controller-runtime/pkg/log"

	workspacev1 "chuangcache.com/ai-saas/api/v1"
)

const (
	// DefaultImage is the default container image to use
	DefaultImage = "nginx:latest"
	// DockerRegistryURL is the Docker registry URL
	DockerRegistryURL = "*************:5000"
	// DockerAPIPort is the Docker API port on nodes
	DockerAPIPort = "22375"
	// WorkspaceFinalizer is the finalizer for workspace resources
	WorkspaceFinalizer = "workspace.chuangcache.com/finalizer"
)

// RegistryResponse represents the response from Docker registry API
type RegistryResponse struct {
	Name string   `json:"name"`
	Tags []string `json:"tags"`
}

// RegistryError represents an error response from Docker registry API
type RegistryError struct {
	Errors []struct {
		Code    string `json:"code"`
		Message string `json:"message"`
		Detail  struct {
			Name string `json:"name"`
		} `json:"detail"`
	} `json:"errors"`
}

// WorkspaceReconciler reconciles a Workspace object
type WorkspaceReconciler struct {
	client.Client
	Scheme *runtime.Scheme
}

// +kubebuilder:rbac:groups=workspace.chuangcache.com,resources=workspaces,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=workspace.chuangcache.com,resources=workspaces/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=workspace.chuangcache.com,resources=workspaces/finalizers,verbs=update
// +kubebuilder:rbac:groups="",resources=pods,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups="",resources=nodes,verbs=get;list;watch

// checkImageInRegistry checks if an image exists in the Docker registry
func (r *WorkspaceReconciler) checkImageInRegistry(ctx context.Context, imageName string) (bool, error) {
	return r.checkImageInRegistryWithURL(ctx, imageName, DockerRegistryURL)
}

// checkImageInRegistryWithURL checks if an image exists in the Docker registry with custom URL (for testing)
func (r *WorkspaceReconciler) checkImageInRegistryWithURL(ctx context.Context, imageName, registryURL string) (bool, error) {
	log := logf.FromContext(ctx)

	url := fmt.Sprintf("http://%s/v2/%s/tags/list", registryURL, imageName)

	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	resp, err := client.Get(url)
	if err != nil {
		log.Error(err, "Failed to check registry", "url", url)
		return false, err
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		var registryResp RegistryResponse
		if err := json.NewDecoder(resp.Body).Decode(&registryResp); err != nil {
			log.Error(err, "Failed to decode registry response")
			return false, err
		}
		return len(registryResp.Tags) > 0, nil
	} else if resp.StatusCode == http.StatusNotFound {
		return false, nil
	}

	return false, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
}

// getImageToUse determines which image to use for the workspace
func (r *WorkspaceReconciler) getImageToUse(ctx context.Context, workspace *workspacev1.Workspace) (string, error) {
	log := logf.FromContext(ctx)

	// Check if custom image exists in registry
	exists, err := r.checkImageInRegistry(ctx, workspace.Name)
	if err != nil {
		log.Error(err, "Failed to check image in registry", "imageName", workspace.Name)
		// Fall back to default image on error
		return DefaultImage, nil
	}

	if exists {
		customImage := fmt.Sprintf("%s/%s:latest", DockerRegistryURL, workspace.Name)
		log.Info("Using custom image from registry", "image", customImage)
		return customImage, nil
	}

	// Use specified image or default
	if workspace.Spec.Image != "" {
		log.Info("Using specified image", "image", workspace.Spec.Image)
		return workspace.Spec.Image, nil
	}

	log.Info("Using default image", "image", DefaultImage)
	return DefaultImage, nil
}

// createPod creates a pod for the workspace
func (r *WorkspaceReconciler) createPod(ctx context.Context, workspace *workspacev1.Workspace, image string) (*corev1.Pod, error) {
	log := logf.FromContext(ctx)

	podName := fmt.Sprintf("%s-pod", workspace.Name)

	pod := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      podName,
			Namespace: workspace.Namespace,
			Labels: map[string]string{
				"app":                            "workspace",
				"workspace.chuangcache.com/name": workspace.Name,
			},
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name:  "workspace",
					Image: image,
					Ports: []corev1.ContainerPort{
						{
							ContainerPort: 80,
							Protocol:      corev1.ProtocolTCP,
						},
					},
				},
			},
			RestartPolicy: corev1.RestartPolicyAlways,
		},
	}

	// Apply resource requirements if specified
	if workspace.Spec.Resources != nil {
		container := &pod.Spec.Containers[0]
		if container.Resources.Limits == nil {
			container.Resources.Limits = make(corev1.ResourceList)
		}
		if container.Resources.Requests == nil {
			container.Resources.Requests = make(corev1.ResourceList)
		}

		// Convert string resources to Quantity (simplified)
		// In production, you'd want proper validation and conversion
	}

	// Apply node selector if specified
	if workspace.Spec.NodeSelector != nil {
		pod.Spec.NodeSelector = workspace.Spec.NodeSelector
	}

	// Set owner reference
	if err := controllerutil.SetControllerReference(workspace, pod, r.Scheme); err != nil {
		return nil, err
	}

	if err := r.Create(ctx, pod); err != nil {
		log.Error(err, "Failed to create pod", "pod", podName)
		return nil, err
	}

	log.Info("Created pod", "pod", podName, "image", image)
	return pod, nil
}

// Reconcile is part of the main kubernetes reconciliation loop
func (r *WorkspaceReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	log := logf.FromContext(ctx)

	// Fetch the Workspace instance
	workspace := &workspacev1.Workspace{}
	err := r.Get(ctx, req.NamespacedName, workspace)
	if err != nil {
		if errors.IsNotFound(err) {
			log.Info("Workspace resource not found. Ignoring since object must be deleted")
			return ctrl.Result{}, nil
		}
		log.Error(err, "Failed to get Workspace")
		return ctrl.Result{}, err
	}

	// Handle deletion
	if workspace.DeletionTimestamp != nil {
		return r.handleDeletion(ctx, workspace)
	}

	// Add finalizer if not present
	if !controllerutil.ContainsFinalizer(workspace, WorkspaceFinalizer) {
		controllerutil.AddFinalizer(workspace, WorkspaceFinalizer)
		return ctrl.Result{}, r.Update(ctx, workspace)
	}

	// Check if pod already exists
	podName := fmt.Sprintf("%s-pod", workspace.Name)
	existingPod := &corev1.Pod{}
	err = r.Get(ctx, types.NamespacedName{Name: podName, Namespace: workspace.Namespace}, existingPod)

	if err != nil && errors.IsNotFound(err) {
		// Pod doesn't exist, create it
		image, err := r.getImageToUse(ctx, workspace)
		if err != nil {
			log.Error(err, "Failed to determine image to use")
			return ctrl.Result{}, err
		}

		pod, err := r.createPod(ctx, workspace, image)
		if err != nil {
			// Update status to failed - get latest version first
			latest := &workspacev1.Workspace{}
			if getErr := r.Get(ctx, types.NamespacedName{Name: workspace.Name, Namespace: workspace.Namespace}, latest); getErr != nil {
				log.Error(getErr, "Failed to get latest workspace for error update")
				return ctrl.Result{RequeueAfter: 5 * time.Second}, err
			}
			latest.Status.Phase = workspacev1.WorkspaceFailed
			latest.Status.Message = fmt.Sprintf("Failed to create pod: %v", err)
			now := metav1.Now()
			latest.Status.LastUpdateTime = &now
			r.Status().Update(ctx, latest)
			return ctrl.Result{}, err
		}

		// Update status to pending using retry logic
		return r.updateWorkspaceStatusAfterPodCreation(ctx, workspace, pod, image)
	} else if err != nil {
		log.Error(err, "Failed to get pod")
		return ctrl.Result{}, err
	}

	// Pod exists, update status based on pod status
	return r.updateWorkspaceStatus(ctx, workspace, existingPod)
}

// updateWorkspaceStatus updates the workspace status based on pod status
func (r *WorkspaceReconciler) updateWorkspaceStatus(ctx context.Context, workspace *workspacev1.Workspace, pod *corev1.Pod) (ctrl.Result, error) {
	// Update node name if pod is scheduled
	if pod.Spec.NodeName != "" && workspace.Status.NodeName != pod.Spec.NodeName {
		workspace.Status.NodeName = pod.Spec.NodeName
	}

	// Update phase based on pod phase
	var newPhase workspacev1.WorkspacePhase
	var message string

	switch pod.Status.Phase {
	case corev1.PodPending:
		newPhase = workspacev1.WorkspacePending
		message = "Pod is pending"
	case corev1.PodRunning:
		newPhase = workspacev1.WorkspaceRunning
		message = "Pod is running"
	case corev1.PodSucceeded:
		newPhase = workspacev1.WorkspaceRunning
		message = "Pod succeeded"
	case corev1.PodFailed:
		newPhase = workspacev1.WorkspaceFailed
		message = "Pod failed"
	default:
		newPhase = workspacev1.WorkspacePending
		message = "Pod status unknown"
	}

	// Update status if changed
	needsUpdate := workspace.Status.Phase != newPhase ||
		workspace.Status.Message != message ||
		workspace.Status.NodeName != pod.Spec.NodeName ||
		workspace.Status.PodName != pod.Name

	if needsUpdate {
		// Use retry logic for status updates
		return r.updateWorkspaceStatusWithRetry(ctx, workspace, newPhase, message, pod)
	}

	// Requeue if pod is not running yet
	if newPhase != workspacev1.WorkspaceRunning {
		return ctrl.Result{RequeueAfter: 10 * time.Second}, nil
	}

	return ctrl.Result{}, nil
}

// updateWorkspaceStatusAfterPodCreation updates workspace status after pod creation
func (r *WorkspaceReconciler) updateWorkspaceStatusAfterPodCreation(ctx context.Context, workspace *workspacev1.Workspace, pod *corev1.Pod, image string) (ctrl.Result, error) {
	log := logf.FromContext(ctx)

	// Try up to 3 times
	for i := 0; i < 3; i++ {
		// Get the latest version to avoid conflicts
		latest := &workspacev1.Workspace{}
		if err := r.Get(ctx, types.NamespacedName{Name: workspace.Name, Namespace: workspace.Namespace}, latest); err != nil {
			log.Error(err, "Failed to get latest workspace for status update")
			return ctrl.Result{RequeueAfter: 5 * time.Second}, nil
		}

		latest.Status.Phase = workspacev1.WorkspacePending
		latest.Status.PodName = pod.Name
		latest.Status.ImageUsed = image
		latest.Status.Message = "Pod created, waiting for it to start"
		now := metav1.Now()
		latest.Status.LastUpdateTime = &now

		if err := r.Status().Update(ctx, latest); err != nil {
			if i == 2 { // Last attempt
				log.Error(err, "Failed to update workspace status after pod creation")
				return ctrl.Result{RequeueAfter: 10 * time.Second}, nil
			}
			log.V(1).Info("Status update failed, retrying", "attempt", i+1, "error", err.Error())
			time.Sleep(time.Duration(i+1) * time.Second) // Exponential backoff
			continue
		}

		log.Info("Updated workspace status after pod creation", "phase", "Pending", "image", image)
		break
	}

	return ctrl.Result{RequeueAfter: 10 * time.Second}, nil
}

// updateWorkspaceStatusWithRetry updates workspace status with retry logic
func (r *WorkspaceReconciler) updateWorkspaceStatusWithRetry(ctx context.Context, workspace *workspacev1.Workspace, phase workspacev1.WorkspacePhase, message string, pod *corev1.Pod) (ctrl.Result, error) {
	log := logf.FromContext(ctx)

	// Try up to 3 times
	for i := 0; i < 3; i++ {
		// Get the latest version to avoid conflicts
		latest := &workspacev1.Workspace{}
		if err := r.Get(ctx, types.NamespacedName{Name: workspace.Name, Namespace: workspace.Namespace}, latest); err != nil {
			log.Error(err, "Failed to get latest workspace for status update")
			return ctrl.Result{RequeueAfter: 5 * time.Second}, nil
		}

		// Update status fields
		latest.Status.Phase = phase
		latest.Status.Message = message
		latest.Status.NodeName = pod.Spec.NodeName
		if latest.Status.PodName == "" {
			latest.Status.PodName = pod.Name
		}
		now := metav1.Now()
		latest.Status.LastUpdateTime = &now

		if err := r.Status().Update(ctx, latest); err != nil {
			if i == 2 { // Last attempt
				log.Error(err, "Failed to update workspace status after retries")
				return ctrl.Result{RequeueAfter: 10 * time.Second}, nil
			}
			log.V(1).Info("Status update failed, retrying", "attempt", i+1, "error", err.Error())
			time.Sleep(time.Duration(i+1) * time.Second) // Exponential backoff
			continue
		}

		log.Info("Updated workspace status", "phase", phase, "message", message)
		break
	}

	// Requeue if pod is not running yet
	if phase != workspacev1.WorkspaceRunning {
		return ctrl.Result{RequeueAfter: 10 * time.Second}, nil
	}

	return ctrl.Result{}, nil
}

// handleDeletion handles workspace deletion with Docker commit and push
func (r *WorkspaceReconciler) handleDeletion(ctx context.Context, workspace *workspacev1.Workspace) (ctrl.Result, error) {
	log := logf.FromContext(ctx)

	if controllerutil.ContainsFinalizer(workspace, WorkspaceFinalizer) {
		// Update status to deleting - get latest version first
		latest := &workspacev1.Workspace{}
		if err := r.Get(ctx, types.NamespacedName{Name: workspace.Name, Namespace: workspace.Namespace}, latest); err != nil {
			log.Error(err, "Failed to get latest workspace for deletion status update")
		} else {
			latest.Status.Phase = workspacev1.WorkspaceDeleting
			latest.Status.Message = "Deleting workspace"
			now := metav1.Now()
			latest.Status.LastUpdateTime = &now
			if err := r.Status().Update(ctx, latest); err != nil {
				log.Error(err, "Failed to update deletion status")
			}
		}

		// Get the pod to find the node
		podName := fmt.Sprintf("%s-pod", workspace.Name)
		pod := &corev1.Pod{}
		err := r.Get(ctx, types.NamespacedName{Name: podName, Namespace: workspace.Namespace}, pod)

		if err == nil && pod.Spec.NodeName != "" {
			// Commit and push the container before deletion
			if err := r.commitAndPushContainer(ctx, workspace, pod); err != nil {
				log.Error(err, "Failed to commit and push container, continuing with deletion")
			}
		}

		// Remove finalizer to allow deletion
		controllerutil.RemoveFinalizer(workspace, WorkspaceFinalizer)
		return ctrl.Result{}, r.Update(ctx, workspace)
	}

	return ctrl.Result{}, nil
}

// commitAndPushContainer commits the running container and pushes to registry
func (r *WorkspaceReconciler) commitAndPushContainer(ctx context.Context, workspace *workspacev1.Workspace, pod *corev1.Pod) error {
	log := logf.FromContext(ctx)

	if pod.Spec.NodeName == "" {
		return fmt.Errorf("pod node name is empty")
	}

	// Get node IP
	node := &corev1.Node{}
	if err := r.Get(ctx, types.NamespacedName{Name: pod.Spec.NodeName}, node); err != nil {
		return fmt.Errorf("failed to get node: %w", err)
	}

	var nodeIP string
	for _, addr := range node.Status.Addresses {
		if addr.Type == corev1.NodeInternalIP {
			nodeIP = addr.Address
			break
		}
	}

	if nodeIP == "" {
		return fmt.Errorf("node IP not found")
	}

	// Find the container ID from pod status
	var containerID string
	for _, containerStatus := range pod.Status.ContainerStatuses {
		if containerStatus.Name == "workspace" {
			// Extract container ID from the format "docker://abc123..."
			if len(containerStatus.ContainerID) > 9 && containerStatus.ContainerID[:9] == "docker://" {
				containerID = containerStatus.ContainerID[9:]
			}
			break
		}
	}

	if containerID == "" {
		return fmt.Errorf("container ID not found")
	}

	// Commit the container using Docker API
	commitURL := fmt.Sprintf("http://%s:%s/containers/%s/commit", nodeIP, DockerAPIPort, containerID)
	newImageName := fmt.Sprintf("%s/%s:latest", DockerRegistryURL, workspace.Name)

	// Create commit request
	commitReq := fmt.Sprintf("%s?repo=%s&tag=latest", commitURL, fmt.Sprintf("%s/%s", DockerRegistryURL, workspace.Name))

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Post(commitReq, "application/json", nil)
	if err != nil {
		return fmt.Errorf("failed to commit container: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		return fmt.Errorf("commit failed with status: %d", resp.StatusCode)
	}

	// Push the image to registry
	pushURL := fmt.Sprintf("http://%s:%s/images/%s/push", nodeIP, DockerAPIPort, newImageName)
	pushResp, err := client.Post(pushURL, "application/json", nil)
	if err != nil {
		log.Error(err, "Failed to push image, but continuing", "image", newImageName)
		return nil // Don't fail deletion if push fails
	}
	defer pushResp.Body.Close()

	log.Info("Successfully committed and pushed container", "image", newImageName, "node", nodeIP)
	return nil
}

// SetupWithManager sets up the controller with the Manager.
func (r *WorkspaceReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&workspacev1.Workspace{}).
		Owns(&corev1.Pod{}).
		Named("workspace").
		Complete(r)
}
